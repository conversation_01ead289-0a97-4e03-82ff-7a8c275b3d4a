"""
Example script để test FlashDocs integration
Sử dụng để tạo slides từ nội dung bài học
"""

import asyncio
import json
from app.services.flashdocs_service import get_flashdocs_service
from app.services.textbook_retrieval_service import get_textbook_retrieval_service


async def test_flashdocs_integration():
    """Test function cho FlashDocs integration"""
    
    # Test 1: L<PERSON>y nội dung bài học
    print("=== Test 1: Lấy nội dung bài học ===")
    textbook_service = get_textbook_retrieval_service()
    
    # Thay thế bằng lesson_id thực tế trong hệ thống
    lesson_id = "example_lesson_id"
    
    try:
        lesson_data = await textbook_service.get_lesson_with_metadata(lesson_id)
        print(f"Lesson data retrieved: {lesson_data.keys()}")
        print(f"Lesson title: {lesson_data.get('lesson_title', 'N/A')}")
        print(f"Book name: {lesson_data.get('book_name', 'N/A')}")
        print(f"Content length: {len(lesson_data.get('lesson_content', ''))}")
    except Exception as e:
        print(f"Error getting lesson data: {e}")
        return
    
    # Test 2: Tạo slides từ template
    print("\n=== Test 2: Tạo slides từ template ===")
    flashdocs_service = get_flashdocs_service()
    
    try:
        # Ví dụ tạo slides từ template
        template_result = await flashdocs_service.create_lesson_slides_from_content(
            lesson_id=lesson_id,
            template_id="your_template_id_here",  # Thay bằng template ID thực tế
            custom_prompt="Create an educational presentation for high school students"
        )
        print(f"Template slides created: {json.dumps(template_result, indent=2)}")
    except Exception as e:
        print(f"Error creating template slides: {e}")
    
    # Test 3: Tạo slides từ library
    print("\n=== Test 3: Tạo slides từ library ===")
    try:
        library_result = await flashdocs_service.create_lesson_slides_from_content(
            lesson_id=lesson_id,
            library_id="your_library_id_here",  # Thay bằng library ID thực tế
            slide_count=8
        )
        print(f"Library slides created: {json.dumps(library_result, indent=2)}")
    except Exception as e:
        print(f"Error creating library slides: {e}")
    
    # Test 4: Tạo slides với hướng dẫn chi tiết
    print("\n=== Test 4: Tạo slides với hướng dẫn chi tiết ===")
    try:
        slide_instructions = [
            {
                "content": "Create a title slide with lesson name and learning objectives",
                "layout": "title layout with large text and subtitle"
            },
            {
                "content": "Introduction to the main topic with key definitions",
                "layout": "text and image layout"
            },
            {
                "content": "Detailed explanation of concept 1 with examples",
                "layout": "two column layout with examples on the right"
            },
            {
                "content": "Detailed explanation of concept 2 with visual aids",
                "layout": "image-heavy layout with bullet points"
            },
            {
                "content": "Practice problems and exercises",
                "layout": "list layout with numbered items"
            },
            {
                "content": "Summary and key takeaways",
                "layout": "summary layout with bullet points"
            },
            {
                "content": "Questions for discussion and homework",
                "layout": "text-only layout with large readable font"
            }
        ]
        
        custom_result = await flashdocs_service.create_custom_lesson_slides(
            lesson_id=lesson_id,
            slide_instructions=slide_instructions,
            template_id="your_template_id_here"  # Hoặc library_id
        )
        print(f"Custom slides created: {json.dumps(custom_result, indent=2)}")
    except Exception as e:
        print(f"Error creating custom slides: {e}")


async def example_api_usage():
    """
    Example sử dụng qua API endpoints
    """
    print("\n=== Example API Usage ===")
    print("1. Tạo slides cơ bản:")
    print("POST /api/v1/flashdocs/create-lesson-slides")
    print(json.dumps({
        "lesson_id": "your_lesson_id",
        "template_id": "your_template_id",
        "slide_count": 8,
        "custom_prompt": "Create engaging slides for high school students"
    }, indent=2))
    
    print("\n2. Tạo slides với hướng dẫn chi tiết:")
    print("POST /api/v1/flashdocs/create-custom-slides")
    print(json.dumps({
        "lesson_id": "your_lesson_id",
        "template_id": "your_template_id",
        "slide_instructions": [
            {
                "content": "Title slide with lesson overview",
                "layout": "centered title layout"
            },
            {
                "content": "Main concepts explanation",
                "layout": "two-column layout"
            }
        ]
    }, indent=2))
    
    print("\n3. Tạo slides nhanh:")
    print("POST /api/v1/flashdocs/quick-lesson-slides/{lesson_id}?template_id=your_template_id&slide_count=6")
    
    print("\n4. Kiểm tra trạng thái:")
    print("GET /api/v1/flashdocs/status/{task_id}")
    
    print("\n5. Download slides:")
    print("GET /api/v1/flashdocs/download/{deck_id}?format=pptx")


if __name__ == "__main__":
    print("FlashDocs Integration Test")
    print("=" * 50)
    
    # Chạy test
    asyncio.run(test_flashdocs_integration())
    
    # Hiển thị example API usage
    asyncio.run(example_api_usage())
    
    print("\n" + "=" * 50)
    print("Để sử dụng thực tế:")
    print("1. Đăng ký FlashDocs API key tại: https://www.flashdocs.com/api/signup")
    print("2. Thêm FLASHDOCS_API_KEY vào environment variables")
    print("3. Upload template hoặc tạo library trong FlashDocs dashboard")
    print("4. Thay thế template_id/library_id trong code")
    print("5. Sử dụng lesson_id thực tế từ hệ thống")
