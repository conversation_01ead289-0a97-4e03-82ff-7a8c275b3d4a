"""
FlashDocs Service - <PERSON><PERSON>ch hợp với FlashDocs API để tạo slide presentations
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional, Union
import httpx
from fastapi import HTTPException

from app.core.config import settings
from app.services.textbook_retrieval_service import get_textbook_retrieval_service

logger = logging.getLogger(__name__)


class FlashDocsService:
    """Service để tích hợp với FlashDocs API"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'FLASHDOCS_API_KEY', None)
        self.base_url = "https://api.flashdocs.com"
        self.textbook_service = None
        
        if not self.api_key:
            logger.warning("FlashDocs API key not configured. Set FLASHDOCS_API_KEY in environment variables.")
    
    def _get_textbook_service(self):
        """Lazy loading textbook service"""
        if not self.textbook_service:
            self.textbook_service = get_textbook_retrieval_service()
        return self.textbook_service
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict] = None,
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Thực hiện HTTP request đến FlashDocs API
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request body data
            params: Query parameters
            
        Returns:
            Response data as dict
            
        Raises:
            HTTPException: Khi request failed
        """
        if not self.api_key:
            raise HTTPException(
                status_code=500, 
                detail="FlashDocs API key not configured"
            )
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=params)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=headers, json=data)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=headers, json=data)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=headers, params=params)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                response.raise_for_status()
                return response.json()
                
        except httpx.HTTPError as e:
            logger.error(f"FlashDocs API request failed: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"FlashDocs API request failed: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error in FlashDocs request: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error: {str(e)}"
            )
    
    async def create_lesson_slides_from_content(
        self,
        lesson_id: str,
        template_id: Optional[str] = None,
        library_id: Optional[str] = None,
        custom_prompt: Optional[str] = None,
        slide_count: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Tạo slides từ nội dung bài học
        
        Args:
            lesson_id: ID của bài học
            template_id: ID của template (nếu sử dụng template)
            library_id: ID của library (nếu sử dụng library)
            custom_prompt: Custom prompt cho việc tạo slides
            slide_count: Số lượng slide mong muốn
            
        Returns:
            Dict chứa thông tin về slide deck được tạo
            
        Raises:
            HTTPException: Khi không thể tạo slides
        """
        try:
            # Lấy nội dung bài học
            textbook_service = self._get_textbook_service()
            lesson_data = await textbook_service.get_lesson_with_metadata(lesson_id)
            
            if not lesson_data or not lesson_data.get("lesson_content"):
                raise HTTPException(
                    status_code=404,
                    detail=f"Lesson content not found for lesson_id: {lesson_id}"
                )
            
            # Chuẩn bị prompt từ nội dung bài học
            lesson_content = lesson_data["lesson_content"]
            
            # Tạo prompt từ nội dung bài học
            if custom_prompt:
                main_prompt = custom_prompt
            else:
                main_prompt = self._generate_lesson_prompt(lesson_content, lesson_data)
            
            # Chuẩn bị data để gọi FlashDocs API
            if template_id:
                # Sử dụng template
                return await self._create_slides_from_template(
                    template_id, main_prompt, lesson_data
                )
            elif library_id:
                # Sử dụng library
                return await self._create_slides_from_library(
                    library_id, main_prompt, lesson_data, slide_count
                )
            else:
                raise HTTPException(
                    status_code=400,
                    detail="Either template_id or library_id must be provided"
                )
                
        except Exception as e:
            logger.error(f"Error creating lesson slides: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create lesson slides: {str(e)}"
            )
    
    def _generate_lesson_prompt(self, lesson_content: str, lesson_data: Dict) -> str:
        """
        Tạo prompt từ nội dung bài học
        
        Args:
            lesson_content: Nội dung bài học
            lesson_data: Metadata của bài học
            
        Returns:
            Generated prompt string
        """
        # Lấy thông tin metadata
        book_name = lesson_data.get("book_name", "Unknown Book")
        lesson_title = lesson_data.get("lesson_title", "Lesson")
        
        # Tạo prompt cơ bản
        prompt = f"""
        Create an educational presentation for the lesson "{lesson_title}" from "{book_name}".
        
        Content to cover:
        {lesson_content[:2000]}...
        
        Please structure the presentation with:
        1. A title slide with lesson name and book reference
        2. Learning objectives slide
        3. Main content slides covering key concepts
        4. Examples and illustrations where appropriate
        5. Summary/conclusion slide
        6. Questions for discussion
        
        Make the presentation engaging and educational, suitable for classroom use.
        Use clear, concise language and organize information logically.
        """
        
        return prompt.strip()
    
    async def _create_slides_from_template(
        self, 
        template_id: str, 
        prompt: str, 
        lesson_data: Dict
    ) -> Dict[str, Any]:
        """
        Tạo slides từ template
        
        Args:
            template_id: ID của template
            prompt: Prompt để tạo nội dung
            lesson_data: Data của bài học
            
        Returns:
            Response từ FlashDocs API
        """
        data = {
            "template_id": template_id,
            "prompt": prompt,
            "variables": {
                "lesson_title": lesson_data.get("lesson_title", ""),
                "book_name": lesson_data.get("book_name", ""),
                "lesson_content": lesson_data.get("lesson_content", "")[:1000]
            }
        }
        
        return await self._make_request("POST", "/api/v1/generate/template", data)
    
    async def _create_slides_from_library(
        self, 
        library_id: str, 
        prompt: str, 
        lesson_data: Dict,
        slide_count: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Tạo slides từ library
        
        Args:
            library_id: ID của library
            prompt: Prompt để tạo nội dung
            lesson_data: Data của bài học
            slide_count: Số lượng slide mong muốn
            
        Returns:
            Response từ FlashDocs API
        """
        data = {
            "library_id": library_id,
            "prompt": prompt,
            "max_slides": slide_count or 10,
            "context": {
                "lesson_title": lesson_data.get("lesson_title", ""),
                "book_name": lesson_data.get("book_name", ""),
                "subject": "Education"
            }
        }
        
        return await self._make_request("POST", "/api/v1/generate/library", data)
    
    async def create_custom_lesson_slides(
        self,
        lesson_id: str,
        slide_instructions: List[Dict[str, str]],
        template_id: Optional[str] = None,
        library_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Tạo slides với hướng dẫn chi tiết cho từng slide
        
        Args:
            lesson_id: ID của bài học
            slide_instructions: List các hướng dẫn cho từng slide
                [{"content": "slide content", "layout": "layout instruction"}, ...]
            template_id: ID template (nếu có)
            library_id: ID library (nếu có)
            
        Returns:
            Response từ FlashDocs API
        """
        try:
            # Lấy nội dung bài học
            textbook_service = self._get_textbook_service()
            lesson_data = await textbook_service.get_lesson_with_metadata(lesson_id)
            
            if not lesson_data:
                raise HTTPException(
                    status_code=404,
                    detail=f"Lesson not found: {lesson_id}"
                )
            
            # Chuẩn bị outline với hướng dẫn chi tiết
            outline = []
            for i, instruction in enumerate(slide_instructions):
                slide_data = {
                    "slide_number": i + 1,
                    "content_instruction": instruction.get("content", ""),
                    "layout_instruction": instruction.get("layout", "")
                }
                outline.append(slide_data)
            
            # Tạo request data
            data = {
                "outline": outline,
                "context": {
                    "lesson_title": lesson_data.get("lesson_title", ""),
                    "book_name": lesson_data.get("book_name", ""),
                    "lesson_content": lesson_data.get("lesson_content", "")[:1500]
                }
            }
            
            if template_id:
                data["template_id"] = template_id
                endpoint = "/api/v1/generate/template/detailed"
            elif library_id:
                data["library_id"] = library_id
                endpoint = "/api/v1/generate/library/detailed"
            else:
                raise HTTPException(
                    status_code=400,
                    detail="Either template_id or library_id required"
                )
            
            return await self._make_request("POST", endpoint, data)
            
        except Exception as e:
            logger.error(f"Error creating custom lesson slides: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create custom slides: {str(e)}"
            )
    
    async def get_slide_status(self, task_id: str) -> Dict[str, Any]:
        """
        Kiểm tra trạng thái của slide generation task
        
        Args:
            task_id: ID của task
            
        Returns:
            Status information
        """
        return await self._make_request("GET", f"/api/v1/tasks/{task_id}")
    
    async def download_slides(self, deck_id: str, format: str = "pptx") -> bytes:
        """
        Download slides đã tạo
        
        Args:
            deck_id: ID của deck
            format: Format file (pptx, pdf, etc.)
            
        Returns:
            File content as bytes
        """
        params = {"format": format}
        
        if not self.api_key:
            raise HTTPException(status_code=500, detail="API key not configured")
        
        headers = {"Authorization": f"Bearer {self.api_key}"}
        url = f"{self.base_url}/api/v1/decks/{deck_id}/download"
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.get(url, headers=headers, params=params)
            response.raise_for_status()
            return response.content


# Singleton instance
_flashdocs_service_instance = None

def get_flashdocs_service() -> FlashDocsService:
    """
    Get singleton instance của FlashDocsService
    
    Returns:
        FlashDocsService: Service instance
    """
    global _flashdocs_service_instance
    if _flashdocs_service_instance is None:
        _flashdocs_service_instance = FlashDocsService()
    return _flashdocs_service_instance
