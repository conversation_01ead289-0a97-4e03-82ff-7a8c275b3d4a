#!/usr/bin/env python3
"""
Test script cho Smart Exam Generation với Celery integration
"""

import asyncio
import json
import time
from typing import Dict, Any
import requests
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Test data
SMART_EXAM_REQUEST = {
    "school": "Trường THPT Test",
    "examCode": "TEST001",
    "grade": 12,
    "subject": "Hoa hoc",
    "examTitle": "Kiểm tra giữa kỳ",
    "duration": 90,
    "outputFormat": "docx",
    "outputLink": "online",
    "matrix": [
        {
            "lessonId": "234",
            "totalQuestions": 6,
            "parts": [
                {
                    "part": 1,
                    "objectives": {
                        "Biết": 2,
                        "Hiểu": 1,
                        "Vận_dụng": 0
                    }
                },
                {
                    "part": 2,
                    "objectives": {
                        "Biết": 1,
                        "Hiểu": 1,
                        "Vận_dụng": 0
                    }
                },
                {
                    "part": 3,
                    "objectives": {
                        "Biết": 0,
                        "Hiểu": 1,
                        "Vận_dụng": 0
                    }
                }
            ]
        }
    ]
}

BASE_URL = "http://localhost:8000"

def test_smart_exam_generation():
    """Test smart exam generation với Celery"""
    print("=== TESTING SMART EXAM GENERATION WITH CELERY ===")
    
    # 1. Tạo task
    print("\n1. Creating smart exam generation task...")
    response = requests.post(
        f"{BASE_URL}/api/v1/exam/generate-smart-exam",
        json=SMART_EXAM_REQUEST,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code != 200:
        print(f"❌ Failed to create task: {response.status_code}")
        print(f"Response: {response.text}")
        return False
    
    result = response.json()
    print(f"✅ Task created successfully!")
    print(f"Task ID: {result.get('task_id')}")
    print(f"Message: {result.get('message')}")
    
    task_id = result.get('task_id')
    if not task_id:
        print("❌ No task_id returned")
        return False
    
    # 2. Theo dõi tiến trình
    print(f"\n2. Monitoring task progress...")
    max_wait_time = 300  # 5 minutes
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        # Check task status
        status_response = requests.get(f"{BASE_URL}/api/v1/tasks/{task_id}/status")
        
        if status_response.status_code != 200:
            print(f"❌ Failed to get task status: {status_response.status_code}")
            break
        
        status_data = status_response.json()
        task_status = status_data.get('status')
        progress = status_data.get('progress', {})
        
        print(f"📊 Status: {task_status}")
        if progress:
            percentage = progress.get('percentage', 0)
            message = progress.get('message', '')
            print(f"📈 Progress: {percentage}% - {message}")
        
        if task_status == 'completed':
            print("✅ Task completed successfully!")
            
            # Get result
            result_response = requests.get(f"{BASE_URL}/api/v1/tasks/{task_id}/result")
            if result_response.status_code == 200:
                result_data = result_response.json()
                print(f"📄 Result summary:")
                
                if result_data.get('success'):
                    exam_data = result_data.get('result', {})
                    statistics = exam_data.get('statistics', {})
                    online_links = exam_data.get('online_links', {})
                    
                    print(f"   - Exam ID: {exam_data.get('exam_id')}")
                    print(f"   - Total questions: {statistics.get('total_questions', 0)}")
                    print(f"   - Generation time: {statistics.get('generation_time_seconds', 0):.2f}s")
                    
                    if online_links:
                        print(f"   - Google Docs link: {online_links.get('google_docs_link', 'N/A')}")
                        print(f"   - PDF link: {online_links.get('pdf_link', 'N/A')}")
                else:
                    print(f"   - Error: {result_data.get('error', 'Unknown error')}")
            
            return True
            
        elif task_status == 'failed':
            print("❌ Task failed!")
            error_info = status_data.get('error_info', {})
            print(f"Error: {error_info.get('error', 'Unknown error')}")
            return False
        
        # Wait before next check
        time.sleep(5)
    
    print("⏰ Task timed out")
    return False

def test_task_status_endpoint():
    """Test task status endpoint với invalid task_id"""
    print("\n=== TESTING TASK STATUS ENDPOINT ===")
    
    # Test với invalid task_id
    response = requests.get(f"{BASE_URL}/api/v1/tasks/invalid-task-id/status")
    print(f"Status for invalid task: {response.status_code}")
    if response.status_code == 200:
        print(f"Response: {response.json()}")

def main():
    """Main test function"""
    print("🚀 Starting Smart Exam Generation Integration Tests")
    print(f"Base URL: {BASE_URL}")
    
    # Test server connectivity
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server not responding properly: {response.status_code}")
            return
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to server: {e}")
        print("Make sure the FastAPI server is running on localhost:8000")
        return
    
    print("✅ Server is running")
    
    # Run tests
    success = test_smart_exam_generation()
    test_task_status_endpoint()
    
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n❌ Some tests failed!")

if __name__ == "__main__":
    main()
