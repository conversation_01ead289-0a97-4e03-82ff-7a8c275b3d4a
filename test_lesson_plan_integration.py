"""
Test script để kiểm tra tích hợ<PERSON> cho lesson plan content generation
"""
import asyncio
import json
import sys
import os

# Add app directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.background_task_processor import background_task_processor
from app.services.mongodb_task_service import mongodb_task_service


async def test_lesson_plan_content_generation():
    """Test lesson plan content generation với Celery"""
    
    print("🧪 Testing Lesson Plan Content Generation Integration...")
    
    # Sample lesson plan JSON structure
    sample_lesson_plan = {
        "id": 1,
        "type": "SECTION",
        "title": "<PERSON><PERSON><PERSON> học mẫu",
        "content": "",
        "status": "ACTIVE",
        "children": [
            {
                "id": 2,
                "type": "PARAGRAPH",
                "title": "<PERSON>ục tiêu bài học",
                "content": "",
                "status": "ACTIVE",
                "children": []
            },
            {
                "id": 3,
                "type": "SECTION",
                "title": "Nội dung chính",
                "content": "",
                "status": "ACTIVE",
                "children": [
                    {
                        "id": 4,
                        "type": "PARAGRAPH",
                        "title": "Phần 1",
                        "content": "",
                        "status": "ACTIVE",
                        "children": []
                    },
                    {
                        "id": 5,
                        "type": "LIST_ITEM",
                        "title": "Hoạt động thực hành",
                        "content": "",
                        "status": "ACTIVE",
                        "children": []
                    }
                ]
            }
        ]
    }
    
    try:
        # 1. Test tạo task
        print("📝 Creating lesson plan content generation task...")
        task_id = await background_task_processor.create_lesson_plan_content_task(
            lesson_plan_json=sample_lesson_plan,
            lesson_id="test_lesson_123"
        )
        
        print(f"✅ Task created successfully with ID: {task_id}")
        
        # 2. Test lấy task status
        print("📊 Checking task status...")
        task_status = await mongodb_task_service.get_task_status(task_id)
        
        if task_status:
            print(f"✅ Task status retrieved:")
            print(f"   - Status: {task_status.get('status')}")
            print(f"   - Progress: {task_status.get('progress', 0)}%")
            print(f"   - Message: {task_status.get('message', 'N/A')}")
            print(f"   - Task Type: {task_status.get('task_type')}")
        else:
            print("❌ Failed to retrieve task status")
            return False
        
        # 3. Test API endpoint structure
        print("🔗 Testing API endpoint structure...")
        endpoints = {
            "check_status": f"/api/v1/tasks/{task_id}/status",
            "get_result": f"/api/v1/tasks/{task_id}/result",
        }
        
        print(f"✅ API endpoints ready:")
        for name, url in endpoints.items():
            print(f"   - {name}: {url}")
        
        print("\n🎉 Integration test completed successfully!")
        print("\n📋 Summary:")
        print(f"   - Task ID: {task_id}")
        print(f"   - Initial Status: {task_status.get('status')}")
        print(f"   - Task Type: lesson_plan_content_generation")
        print(f"   - Queue: default")
        print(f"   - Celery Task: app.tasks.lesson_plan_tasks.process_lesson_plan_content_generation")
        
        print("\n🚀 Next steps:")
        print("   1. Start Celery worker to process the task")
        print("   2. Monitor progress via /api/v1/tasks/{task_id}/status")
        print("   3. Get result via /api/v1/tasks/{task_id}/result when completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_task_mapping():
    """Test task mapping trong celery_task_service"""
    
    print("\n🔧 Testing Task Mapping...")
    
    try:
        from app.services.celery_task_service import celery_task_service
        
        # Test task name mapping
        task_name = celery_task_service._get_celery_task_name("lesson_plan_content_generation")
        expected_name = "app.tasks.lesson_plan_tasks.process_lesson_plan_content_generation"
        
        if task_name == expected_name:
            print(f"✅ Task name mapping correct: {task_name}")
        else:
            print(f"❌ Task name mapping incorrect. Expected: {expected_name}, Got: {task_name}")
            return False
        
        # Test queue mapping
        queue_name = celery_task_service._get_queue_for_task("lesson_plan_content_generation")
        expected_queue = "default"
        
        if queue_name == expected_queue:
            print(f"✅ Queue mapping correct: {queue_name}")
        else:
            print(f"❌ Queue mapping incorrect. Expected: {expected_queue}, Got: {queue_name}")
            return False
        
        print("✅ Task mapping test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Task mapping test failed: {e}")
        return False


async def main():
    """Main test function"""
    
    print("🔥 Starting Lesson Plan Content Generation Integration Tests")
    print("=" * 60)
    
    # Test 1: Task mapping
    mapping_success = await test_task_mapping()
    
    # Test 2: Integration test
    integration_success = await test_lesson_plan_content_generation()
    
    print("\n" + "=" * 60)
    if mapping_success and integration_success:
        print("🎉 ALL TESTS PASSED! Integration is ready.")
        print("\n💡 To complete the setup:")
        print("   1. Start Celery worker: start_celery_worker.bat")
        print("   2. Start FastAPI server: start_fastapi.bat")
        print("   3. Test the API endpoint: POST /api/v1/lesson-plan/generate-lesson-plan-content")
    else:
        print("❌ SOME TESTS FAILED! Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
