# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Testing
.coverage
htmlcov/
.pytest_cache/

# OS specific
.DS_Store
Thumbs.db

# PlanBookAI specific
temp_uploads/
data/
chroma_db/
tessdata/
*.pyc
*.pyo
*.pyd
__pycache__/
.pytest_cache/

# Celery
celerybeat-schedule
celerybeat.pid

# Docker volumes
redis_data/
mongodb_data/

# Temporary files
*.tmp
*.temp
*.bak

# Environment files
.env.local
.env.production
.env.development

# API Keys (backup)
.env.example.backup
google-credentials.json