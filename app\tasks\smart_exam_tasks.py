"""
Celery tasks cho Smart Exam Generation
Xử lý tạo đề thi thông minh bất đồng bộ với progress tracking chi tiết
"""

import logging
import asyncio
from typing import Dict, Any
from celery import current_task

from app.core.celery_app import celery_app
from app.services.mongodb_task_service import get_mongodb_task_service, TaskType
from app.services.smart_exam_generation_service import get_smart_exam_generation_service
from app.services.exam_content_service import get_exam_content_service
from app.services.smart_exam_docx_service import get_smart_exam_docx_service
from app.services.google_drive_service import get_google_drive_service
from app.models.smart_exam_models import SmartExamRequest

logger = logging.getLogger(__name__)


def run_async_task(coro):
    """Helper function to run async code in Celery worker"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    return loop.run_until_complete(coro)


@celery_app.task(name="app.tasks.smart_exam_tasks.process_smart_exam_generation", bind=True)
def process_smart_exam_generation(self, task_id: str) -> Dict[str, Any]:
    """
    Celery task xử lý tạo đề thi thông minh với progress tracking chi tiết
    
    Args:
        task_id: ID của task trong MongoDB
        
    Returns:
        Dict chứa kết quả tạo đề thi
    """
    logger.info(f"Starting smart exam generation task: {task_id}")
    
    try:
        # Khởi tạo MongoDB task service
        task_service = get_mongodb_task_service()
        logger.info("MongoDB Task Service initialized successfully")
        
        def update_progress(percentage: int, message: str):
            """Callback để cập nhật progress"""
            logger.info(f"Task {task_id}: {percentage}% - {message}")
            run_async_task(task_service.update_task_progress(
                task_id=task_id,
                progress=percentage,
                message=message
            ))
            
            # Cập nhật Celery task state
            current_task.update_state(
                state='PROGRESS',
                meta={'progress': percentage, 'message': message}
            )
        
        # Bước 1: Lấy thông tin task và validate
        update_progress(5, "Đang khởi tạo và kiểm tra thông tin đề thi...")
        
        task_data = run_async_task(task_service.get_task(task_id))
        if not task_data:
            raise Exception(f"Task {task_id} not found")
            
        request_data = task_data.get("request_data", {})
        exam_request = SmartExamRequest(**request_data)
        
        update_progress(10, f"Đã xác thực thông tin đề thi: {exam_request.subject} - Lớp {exam_request.grade}")
        
        # Bước 2: Lấy lesson IDs từ ma trận
        lesson_ids = [lesson.lessonId for lesson in exam_request.matrix]
        total_lessons = len(lesson_ids)
        update_progress(15, f"Đã phân tích ma trận đề thi: {total_lessons} bài học cần xử lý")
        
        # Bước 3: Tìm kiếm nội dung bài học
        update_progress(20, "Đang tìm kiếm nội dung bài học từ cơ sở dữ liệu...")
        
        exam_content_service = get_exam_content_service()
        lesson_content = run_async_task(
            exam_content_service.get_multiple_lessons_content_for_exam(lesson_ids=lesson_ids)
        )
        
        if not lesson_content or not lesson_content.get("success", False):
            raise Exception("Không thể tìm thấy nội dung bài học")
            
        content_data = lesson_content.get("content", {})
        found_lessons = len(content_data)
        update_progress(30, f"Đã tìm thấy nội dung cho {found_lessons}/{total_lessons} bài học")
        
        # Bước 4: Tạo đề thi với progress tracking chi tiết
        update_progress(35, "Bắt đầu tạo câu hỏi đề thi bằng AI...")
        
        smart_exam_service = get_smart_exam_generation_service()
        
        def exam_progress_callback(step: str, current: int, total: int, detail: str = ""):
            """Callback chi tiết cho quá trình tạo đề thi"""
            if total > 0:
                step_progress = int((current / total) * 100)
                overall_progress = 35 + int((step_progress / 100) * 30)  # 35-65% cho việc tạo đề thi
            else:
                overall_progress = 50
                
            message = f"{step}"
            if detail:
                message += f" - {detail}"
            if total > 0:
                message += f" ({current}/{total})"
                
            update_progress(overall_progress, message)
        
        exam_result = run_async_task(
            smart_exam_service.generate_smart_exam_with_progress(
                exam_request=exam_request,
                lesson_content=content_data,
                progress_callback=exam_progress_callback
            )
        )
        
        if not exam_result.get("success", False):
            raise Exception(f"Tạo đề thi thất bại: {exam_result.get('error', 'Unknown error')}")
            
        total_questions = exam_result.get("total_generated", 0)
        update_progress(65, f"Đã tạo thành công {total_questions} câu hỏi")
        
        # Bước 5: Tạo file DOCX
        update_progress(70, "Đang tạo file DOCX cho đề thi...")
        
        smart_exam_docx_service = get_smart_exam_docx_service()
        docx_result = run_async_task(
            smart_exam_docx_service.create_smart_exam_docx(
                exam_data=exam_result,
                exam_request=exam_request.model_dump()
            )
        )
        
        if not docx_result.get("success", False):
            raise Exception(f"Tạo file DOCX thất bại: {docx_result.get('error', 'Unknown error')}")
            
        file_path = docx_result.get("file_path")
        filename = docx_result.get("filename")
        update_progress(80, f"Đã tạo file DOCX: {filename}")
        
        # Bước 6: Upload lên Google Drive
        update_progress(85, "Đang upload file lên Google Drive...")
        
        google_drive_service = get_google_drive_service()
        upload_result = run_async_task(
            google_drive_service.upload_and_get_online_links(
                file_path=file_path,
                filename=filename
            )
        )
        
        if not upload_result.get("success", False):
            raise Exception(f"Upload Google Drive thất bại: {upload_result.get('error', 'Unknown error')}")
            
        online_links = upload_result.get("online_links", {})
        update_progress(95, "Đã upload thành công lên Google Drive")
        
        # Bước 7: Hoàn thành
        update_progress(100, "Hoàn thành tạo đề thi thông minh")
        
        # Kết quả cuối cùng
        final_result = {
            "success": True,
            "exam_id": exam_result.get("exam_id"),
            "online_links": online_links,
            "statistics": exam_result.get("statistics", {}),
            "total_questions": total_questions,
            "task_id": task_id,
            "processing_info": {
                "total_lessons_processed": found_lessons,
                "total_lessons_requested": total_lessons,
                "processing_method": "celery_smart_exam_generation"
            }
        }
        
        logger.info(f"Smart exam generation task {task_id} completed successfully")
        return final_result
        
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Error in smart exam generation task {task_id}: {error_msg}")
        
        # Cập nhật lỗi
        try:
            task_service = get_mongodb_task_service()
            run_async_task(task_service.update_task_progress(
                task_id=task_id,
                progress=0,
                message=f"Lỗi: {error_msg}",
                status="failed"
            ))
        except Exception as update_error:
            logger.error(f"Failed to update task error status: {update_error}")
        
        return {
            "success": False,
            "error": error_msg,
            "task_id": task_id,
            "processing_info": {
                "processing_method": "celery_smart_exam_generation"
            }
        }
