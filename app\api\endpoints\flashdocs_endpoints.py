"""
FlashDocs API Endpoints - Tạo slides từ nội dung bài học
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Response
from pydantic import BaseModel, Field

from app.services.flashdocs_service import get_flashdocs_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/flashdocs", tags=["FlashDocs"])


class SlideInstructionModel(BaseModel):
    """Model cho hướng dẫn slide"""
    content: str = Field(..., description="Nội dung hướng dẫn cho slide")
    layout: Optional[str] = Field(None, description="Hướng dẫn layout cho slide")


class CreateLessonSlidesRequest(BaseModel):
    """Model cho request tạo slides từ bài học"""
    lesson_id: str = Field(..., description="ID của bài học")
    template_id: Optional[str] = Field(None, description="ID của template FlashDocs")
    library_id: Optional[str] = Field(None, description="ID của library FlashDocs")
    custom_prompt: Optional[str] = Field(None, description="Custom prompt cho việc tạo slides")
    slide_count: Optional[int] = Field(10, description="Số lượng slide mong muốn", ge=1, le=50)


class CreateCustomSlidesRequest(BaseModel):
    """Model cho request tạo slides với hướng dẫn chi tiết"""
    lesson_id: str = Field(..., description="ID của bài học")
    slide_instructions: List[SlideInstructionModel] = Field(..., description="Hướng dẫn cho từng slide")
    template_id: Optional[str] = Field(None, description="ID của template FlashDocs")
    library_id: Optional[str] = Field(None, description="ID của library FlashDocs")


@router.post("/create-lesson-slides")
async def create_lesson_slides(request: CreateLessonSlidesRequest) -> Dict[str, Any]:
    """
    Tạo slides presentation từ nội dung bài học
    
    Args:
        request: Request data chứa lesson_id và các tùy chọn
        
    Returns:
        Dict chứa thông tin về slide deck được tạo
        
    Raises:
        HTTPException: Khi không thể tạo slides
    """
    try:
        flashdocs_service = get_flashdocs_service()
        
        result = await flashdocs_service.create_lesson_slides_from_content(
            lesson_id=request.lesson_id,
            template_id=request.template_id,
            library_id=request.library_id,
            custom_prompt=request.custom_prompt,
            slide_count=request.slide_count
        )
        
        logger.info(f"Successfully created slides for lesson: {request.lesson_id}")
        
        return {
            "success": True,
            "message": "Slides created successfully",
            "data": result,
            "lesson_id": request.lesson_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating lesson slides: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create lesson slides: {str(e)}"
        )


@router.post("/create-custom-slides")
async def create_custom_slides(request: CreateCustomSlidesRequest) -> Dict[str, Any]:
    """
    Tạo slides với hướng dẫn chi tiết cho từng slide
    
    Args:
        request: Request data chứa lesson_id và slide instructions
        
    Returns:
        Dict chứa thông tin về slide deck được tạo
    """
    try:
        flashdocs_service = get_flashdocs_service()
        
        # Convert Pydantic models to dict
        slide_instructions = [
            {
                "content": inst.content,
                "layout": inst.layout or ""
            }
            for inst in request.slide_instructions
        ]
        
        result = await flashdocs_service.create_custom_lesson_slides(
            lesson_id=request.lesson_id,
            slide_instructions=slide_instructions,
            template_id=request.template_id,
            library_id=request.library_id
        )
        
        logger.info(f"Successfully created custom slides for lesson: {request.lesson_id}")
        
        return {
            "success": True,
            "message": "Custom slides created successfully",
            "data": result,
            "lesson_id": request.lesson_id,
            "slide_count": len(slide_instructions)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating custom slides: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create custom slides: {str(e)}"
        )


@router.get("/status/{task_id}")
async def get_slide_status(task_id: str) -> Dict[str, Any]:
    """
    Kiểm tra trạng thái của slide generation task
    
    Args:
        task_id: ID của task
        
    Returns:
        Status information của task
    """
    try:
        flashdocs_service = get_flashdocs_service()
        result = await flashdocs_service.get_slide_status(task_id)
        
        return {
            "success": True,
            "task_id": task_id,
            "status": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting slide status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get slide status: {str(e)}"
        )


@router.get("/download/{deck_id}")
async def download_slides(
    deck_id: str, 
    format: str = "pptx"
) -> Response:
    """
    Download slides đã tạo
    
    Args:
        deck_id: ID của deck
        format: Format file (pptx, pdf, etc.)
        
    Returns:
        File content
    """
    try:
        flashdocs_service = get_flashdocs_service()
        file_content = await flashdocs_service.download_slides(deck_id, format)
        
        # Xác định content type dựa trên format
        content_types = {
            "pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "pdf": "application/pdf",
            "png": "image/png",
            "jpg": "image/jpeg",
            "jpeg": "image/jpeg"
        }
        
        content_type = content_types.get(format.lower(), "application/octet-stream")
        
        return Response(
            content=file_content,
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename=lesson_slides_{deck_id}.{format}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading slides: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to download slides: {str(e)}"
        )


@router.post("/quick-lesson-slides/{lesson_id}")
async def quick_create_lesson_slides(
    lesson_id: str,
    template_id: Optional[str] = None,
    library_id: Optional[str] = None,
    slide_count: int = 8
) -> Dict[str, Any]:
    """
    Endpoint nhanh để tạo slides từ lesson_id
    
    Args:
        lesson_id: ID của bài học
        template_id: ID template (optional)
        library_id: ID library (optional)
        slide_count: Số lượng slide
        
    Returns:
        Thông tin slides được tạo
    """
    try:
        flashdocs_service = get_flashdocs_service()
        
        result = await flashdocs_service.create_lesson_slides_from_content(
            lesson_id=lesson_id,
            template_id=template_id,
            library_id=library_id,
            slide_count=slide_count
        )
        
        return {
            "success": True,
            "message": f"Quick slides created for lesson {lesson_id}",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Error in quick slide creation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create quick slides: {str(e)}"
        )
