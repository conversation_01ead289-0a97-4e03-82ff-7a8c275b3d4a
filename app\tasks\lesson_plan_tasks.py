"""
Celery tasks cho lesson plan content generation
"""
import asyncio
from typing import Dict, Any
from celery import current_task

from app.core.celery_app import celery_app
from app.core.logging_config import logger
from app.services.mongodb_task_service import mongodb_task_service
from app.services.lesson_plan_content_service import lesson_plan_content_service
from app.tasks.utils import run_async_task, task_with_retry


@celery_app.task(name="app.tasks.lesson_plan_tasks.process_lesson_plan_content_generation", bind=True)
def process_lesson_plan_content_generation(self, task_id: str) -> Dict[str, Any]:
    """
    Celery task xử lý sinh nội dung giáo án
    
    Args:
        task_id: ID của task trong MongoDB
        
    Returns:
        Dict kết quả xử lý
    """
    logger.info(f"Starting lesson plan content generation task: {task_id}")
    
    try:
        # Update Celery state
        self.update_state(
            state="PROGRESS",
            meta={"progress": 0, "message": "Starting lesson plan content generation..."}
        )
        
        # Run async implementation
        result = run_async_task(_process_lesson_plan_content_generation_async(task_id))
        logger.info(f"Lesson plan content generation task {task_id} completed successfully")
        return result
        
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Error in lesson plan content generation task {task_id}: {error_msg}")
        
        # Mark task as failed in MongoDB
        run_async_task(mongodb_task_service.mark_task_failed(task_id, error_msg))
        
        # Update Celery state
        self.update_state(state="FAILURE", meta={"error": error_msg})
        
        raise


async def _process_lesson_plan_content_generation_async(task_id: str) -> Dict[str, Any]:
    """Async implementation của lesson plan content generation"""
    
    # Get task from MongoDB
    task = await mongodb_task_service.get_task_status(task_id)
    if not task:
        raise Exception(f"Task {task_id} not found in MongoDB")
    
    task_data = task.get("data", {})
    lesson_plan_json = task_data.get("lesson_plan_json")
    lesson_id = task_data.get("lesson_id")
    
    if not lesson_plan_json:
        raise Exception("lesson_plan_json is required in task data")
    
    # Mark task as processing
    await mongodb_task_service.mark_task_processing(task_id)
    
    try:
        # Update progress: Starting analysis
        await mongodb_task_service.update_task_progress(
            task_id, 10, "Analyzing lesson plan structure..."
        )
        
        # Count total nodes to process for progress tracking
        total_nodes = _count_nodes_recursive(lesson_plan_json)
        logger.info(f"Total nodes to process: {total_nodes}")
        
        # Update progress: Structure analyzed
        await mongodb_task_service.update_task_progress(
            task_id, 20, f"Found {total_nodes} nodes to process. Starting content generation..."
        )
        
        # Process lesson plan with progress tracking
        processed_nodes = 0
        
        async def progress_callback(current_node: int, total: int, message: str = ""):
            nonlocal processed_nodes
            processed_nodes = current_node
            # Progress from 20% to 90% based on processed nodes
            progress = 20 + int((current_node / total) * 70)
            await mongodb_task_service.update_task_progress(
                task_id, progress, message or f"Processing node {current_node}/{total}..."
            )
        
        # Generate lesson plan content with progress tracking
        result = await lesson_plan_content_service.generate_lesson_plan_content_with_progress(
            lesson_plan_json=lesson_plan_json,
            lesson_id=lesson_id,
            progress_callback=progress_callback,
            total_nodes=total_nodes
        )
        
        # Update progress: Finalizing
        await mongodb_task_service.update_task_progress(
            task_id, 95, "Finalizing lesson plan content..."
        )
        
        # Prepare final result
        final_result = {
            "success": result["success"],
            "lesson_plan": result.get("lesson_plan"),
            "statistics": result.get("statistics", {}),
            "task_id": task_id,
            "processing_info": {
                "total_nodes_processed": processed_nodes,
                "total_nodes_found": total_nodes,
                "processing_method": "celery_lesson_plan_content_generation"
            }
        }
        
        if not result["success"]:
            final_result["error"] = result.get("error", "Unknown error occurred")
        
        # Mark task as completed
        await mongodb_task_service.mark_task_completed(task_id, final_result)
        
        logger.info(f"Lesson plan content generation task {task_id} completed successfully")
        return final_result
        
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Error processing lesson plan content generation task {task_id}: {error_msg}")
        
        # Create error result
        error_result = {
            "success": False,
            "error": error_msg,
            "task_id": task_id,
            "processing_info": {
                "total_nodes_processed": processed_nodes,
                "total_nodes_found": total_nodes,
                "processing_method": "celery_lesson_plan_content_generation_failed"
            }
        }
        
        # Mark task as failed
        await mongodb_task_service.mark_task_failed(task_id, error_msg)
        
        raise Exception(error_msg)


def _count_nodes_recursive(node: Dict[str, Any]) -> int:
    """
    Đếm tổng số nodes trong cây lesson plan để tracking progress
    
    Args:
        node: Node gốc của lesson plan
        
    Returns:
        int: Tổng số nodes
    """
    count = 1  # Count current node
    
    children = node.get("children", [])
    if children:
        for child in children:
            count += _count_nodes_recursive(child)
    
    return count
