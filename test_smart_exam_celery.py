"""
Test Smart Exam với Celery Worker thực tế
"""

import asyncio
import logging
import time

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_smart_exam_with_celery():
    """Test smart exam với Celery worker thực tế"""
    
    print("🚀 Test Smart Exam với Celery Worker")
    print("=" * 50)
    
    try:
        # Tạo task mới
        from app.services.background_task_processor import get_background_task_processor
        
        sample_request = {
            "school": "Trường THPT Test Celery",
            "grade": 12,
            "subject": "Hóa học",
            "examTitle": "Kiểm tra Celery Integration",
            "duration": 45,
            "examCode": "0001",
            "outputFormat": "docx",
            "outputLink": "online",
            "matrix": [
                {
                    "lessonId": "test_lesson_celery",
                    "totalQuestions": 3,
                    "parts": [
                        {
                            "part": 1,
                            "objectives": {
                                "Biết": 1,
                                "Hiểu": 0,
                                "Vận_dụng": 0
                            }
                        },
                        {
                            "part": 2,
                            "objectives": {
                                "Biết": 0,
                                "Hiểu": 1,
                                "Vận_dụng": 0
                            }
                        },
                        {
                            "part": 3,
                            "objectives": {
                                "Biết": 0,
                                "Hiểu": 0,
                                "Vận_dụng": 1
                            }
                        }
                    ]
                }
            ]
        }
        
        background_processor = get_background_task_processor()
        print("📝 Tạo task mới...")
        
        task_result = await background_processor.create_smart_exam_task(
            request_data=sample_request
        )
        
        if not task_result.get("success", False):
            print(f"❌ Không thể tạo task: {task_result.get('error')}")
            return False
        
        task_id = task_result.get("task_id")
        print(f"✅ Task được tạo: {task_id}")
        
        # Theo dõi progress
        from app.services.mongodb_task_service import get_mongodb_task_service
        task_service = get_mongodb_task_service()
        
        print("\n📊 Theo dõi progress...")
        max_wait = 300  # 5 phút
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            task_status = await task_service.get_task_status(task_id)
            
            if task_status:
                status = task_status.get('status')
                progress = task_status.get('progress', 0)
                message = task_status.get('message', '')
                
                print(f"   Status: {status} | Progress: {progress}% | Message: {message}")
                
                if status == 'completed':
                    print("🎉 Task hoàn thành thành công!")
                    
                    # Lấy kết quả
                    result = task_status.get('result', {})
                    if result.get('success'):
                        print("✅ Kết quả:")
                        print(f"   - Exam ID: {result.get('exam_id')}")
                        print(f"   - Message: {result.get('message')}")
                        print(f"   - Online Links: {result.get('online_links', {})}")
                        print(f"   - Statistics: {result.get('statistics', {})}")
                        print(f"   - Processing Info: {result.get('processing_info', {})}")
                    else:
                        print(f"❌ Task hoàn thành nhưng có lỗi: {result.get('error')}")
                    
                    return True
                    
                elif status == 'failed':
                    print(f"❌ Task thất bại: {message}")
                    return False
                    
            else:
                print("❌ Không thể lấy task status")
                return False
            
            # Đợi 5 giây trước khi check lại
            await asyncio.sleep(5)
        
        print(f"⏰ Timeout sau {max_wait} giây")
        return False
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_smart_exam_with_celery())
    if success:
        print("\n✅ Test thành công!")
    else:
        print("\n❌ Test thất bại!")
