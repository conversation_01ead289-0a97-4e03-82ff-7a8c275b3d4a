"""
Test script cho Smart Exam Celery Integration
Kiểm tra tích hợp Celery cho API generate_smart_exam
"""

import asyncio
import logging
from typing import Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_smart_exam_integration():
    """Test tích hợp smart exam với Celery"""
    
    print("🔥 Bắt đầu kiểm tra tích hợp Smart Exam với Celery")
    print("=" * 60)
    
    try:
        # Test 1: Kiểm tra task mapping
        print("🔧 Kiểm tra Task Mapping...")
        from app.services.celery_task_service import CeleryTaskService
        
        celery_service = CeleryTaskService()
        
        # Kiểm tra task name mapping
        task_name = celery_service._get_celery_task_name("smart_exam_generation")
        expected_task_name = "app.tasks.smart_exam_tasks.process_smart_exam_generation"
        
        if task_name == expected_task_name:
            print(f"✅ Task name mapping đúng: {task_name}")
        else:
            print(f"❌ Task name mapping sai: {task_name} != {expected_task_name}")
            return False
        
        # Kiểm tra queue mapping
        queue_name = celery_service._get_queue_for_task("smart_exam_generation")
        expected_queue = "default"
        
        if queue_name == expected_queue:
            print(f"✅ Queue mapping đúng: {queue_name}")
        else:
            print(f"❌ Queue mapping sai: {queue_name} != {expected_queue}")
            return False
        
        print("✅ Task mapping test passed!")
        
        # Test 2: Kiểm tra tạo task
        print("\n🧪 Kiểm tra tạo Smart Exam Task...")
        from app.services.background_task_processor import get_background_task_processor
        
        # Sample request data
        sample_request = {
            "school": "Trường THPT Test",
            "grade": 12,
            "subject": "Hóa học",
            "examTitle": "Kiểm tra giữa kỳ 1",
            "duration": 45,
            "examCode": "0001",
            "outputFormat": "docx",
            "outputLink": "online",
            "matrix": [
                {
                    "lessonId": "test_lesson_123",
                    "totalQuestions": 5,
                    "parts": [
                        {
                            "part": 1,
                            "objectives": {
                                "Biết": 2,
                                "Hiểu": 0,
                                "Vận_dụng": 0
                            }
                        },
                        {
                            "part": 2,
                            "objectives": {
                                "Biết": 0,
                                "Hiểu": 2,
                                "Vận_dụng": 0
                            }
                        },
                        {
                            "part": 3,
                            "objectives": {
                                "Biết": 0,
                                "Hiểu": 0,
                                "Vận_dụng": 1
                            }
                        }
                    ]
                }
            ]
        }
        
        background_processor = get_background_task_processor()
        print("📝 Tạo smart exam task...")
        
        task_result = await background_processor.create_smart_exam_task(
            request_data=sample_request
        )
        
        if not task_result.get("success", False):
            print(f"❌ Không thể tạo task: {task_result.get('error')}")
            return False
        
        task_id = task_result.get("task_id")
        print(f"✅ Task được tạo thành công với ID: {task_id}")
        
        # Test 3: Kiểm tra task status
        print("\n📊 Kiểm tra task status...")
        from app.services.mongodb_task_service import get_mongodb_task_service
        
        task_service = get_mongodb_task_service()
        task_status = await task_service.get_task_status(task_id)
        
        if task_status:
            print("✅ Task status được lấy thành công:")
            print(f"   - Status: {task_status.get('status')}")
            print(f"   - Progress: {task_status.get('progress')}%")
            print(f"   - Message: {task_status.get('message')}")
            print(f"   - Task Type: {task_status.get('task_type')}")
        else:
            print(f"❌ Không thể lấy task status cho task: {task_id}")
            return False
        
        # Test 4: Kiểm tra API endpoints
        print("\n🔗 Kiểm tra API endpoint structure...")
        print("✅ API endpoints sẵn sàng:")
        print(f"   - check_status: /api/v1/tasks/{task_id}/status")
        print(f"   - get_result: /api/v1/tasks/{task_id}/result")
        
        print(f"\n🎉 Integration test hoàn thành thành công!")
        
        print(f"\n📋 Tóm tắt:")
        print(f"   - Task ID: {task_id}")
        print(f"   - Initial Status: {task_status.get('status')}")
        print(f"   - Task Type: smart_exam_generation")
        print(f"   - Queue: default")
        print(f"   - Celery Task: app.tasks.smart_exam_tasks.process_smart_exam_generation")
        
        print(f"\n🚀 Các bước tiếp theo:")
        print(f"   1. Khởi động Celery worker để xử lý task")
        print(f"   2. Theo dõi progress qua /api/v1/tasks/{task_id}/status")
        print(f"   3. Lấy kết quả qua /api/v1/tasks/{task_id}/result khi hoàn thành")
        
        print("\n" + "=" * 60)
        print("🎉 TẤT CẢ TESTS ĐÃ PASS! Integration sẵn sàng.")
        
        print(f"\n💡 Để hoàn thành setup:")
        print(f"   1. Khởi động Celery worker: start_celery_worker.bat")
        print(f"   2. Khởi động FastAPI server: start_fastapi.bat")
        print(f"   3. Test API endpoint: POST /api/v1/exam/generate-smart-exam")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi trong quá trình test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_smart_exam_integration())
    if success:
        print("\n✅ Integration test thành công!")
    else:
        print("\n❌ Integration test thất bại!")
