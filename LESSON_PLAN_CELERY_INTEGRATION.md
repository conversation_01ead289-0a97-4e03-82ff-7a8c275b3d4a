# Lesson Plan Content Generation - Celery Integration

## Tổng quan

Đã tích hợp thành công API `generate_lesson_plan_content` với Celery để xử lý bất đồng bộ và tracking progress, tương tự như API `quick_textbook_analysis`.

## C<PERSON>c thay đổi đã thực hiện

### 1. Tạo Celery Task mới

**File:** `app/tasks/lesson_plan_tasks.py`
- Tạo task `process_lesson_plan_content_generation` 
- Hỗ trợ progress tracking với callback
- Xử lý lỗi và cập nhật trạng thái MongoDB
- Đếm tổng số nodes để tính progress chính xác

### 2. Cập nhật API Endpoint

**File:** `app/api/endpoints/lesson_plan.py`
- Thay đổi endpoint `/generate-lesson-plan-content` từ xử lý đồng bộ sang bất đồng bộ
- Trả về `task_id` thay vì kết quả trực tiếp
- <PERSON><PERSON> cấp endpoints để check status và lấy kết quả

### 3. Thêm Task Mapping

**File:** `app/services/celery_task_service.py`
- Thêm mapping `lesson_plan_content_generation` → `app.tasks.lesson_plan_tasks.process_lesson_plan_content_generation`
- Sử dụng queue `default` cho task này

### 4. Cập nhật Background Task Processor

**File:** `app/services/background_task_processor.py`
- Thêm method `create_lesson_plan_content_task()`
- Tạo và dispatch task đến Celery worker

### 5. Thêm Task Type

**File:** `app/services/mongodb_task_service.py`
- Thêm `LESSON_PLAN_CONTENT_GENERATION = "lesson_plan_content_generation"` vào TaskType enum

### 6. Cập nhật Lesson Plan Content Service

**File:** `app/services/lesson_plan_content_service.py`
- Thêm method `generate_lesson_plan_content_with_progress()`
- Thêm method `_process_lesson_plan_recursive_with_progress()`
- Hỗ trợ progress callback để update tiến độ

### 7. Đăng ký Task trong Celery App

**File:** `app/core/celery_app.py`
- Thêm `"app.tasks.lesson_plan_tasks"` vào danh sách include

## Cách sử dụng

### 1. API Request

```bash
POST /api/v1/lesson-plan/generate-lesson-plan-content
Content-Type: application/json

{
    "lesson_plan_json": {
        "id": 1,
        "type": "SECTION",
        "title": "Bài học mẫu",
        "content": "",
        "status": "ACTIVE",
        "children": [...]
    },
    "lesson_id": "optional_lesson_id"
}
```

### 2. API Response

```json
{
    "success": true,
    "task_id": "abc-123-def",
    "status": "processing",
    "message": "Lesson plan content generation task created successfully...",
    "endpoints": {
        "check_status": "/api/v1/tasks/abc-123-def/status",
        "get_result": "/api/v1/tasks/abc-123-def/result"
    }
}
```

### 3. Check Progress

```bash
GET /api/v1/tasks/{task_id}/status
```

Response:
```json
{
    "task_id": "abc-123-def",
    "status": "processing",
    "progress": 45,
    "message": "Processing node 3/7...",
    "progress_history": [...]
}
```

### 4. Get Result

```bash
GET /api/v1/tasks/{task_id}/result
```

## Progress Tracking

Task sẽ báo cáo progress theo các bước:

1. **0-10%**: Analyzing lesson plan structure
2. **10-20%**: Structure analyzed, starting content generation
3. **20-90%**: Processing nodes (tính theo số nodes đã xử lý)
4. **90-95%**: Finalizing lesson plan content
5. **100%**: Completed

## Testing

### 1. Integration Test
```bash
python test_lesson_plan_integration.py
```

### 2. API Test (cần server chạy)
```bash
python test_api_endpoint.py
```

## Khởi động hệ thống

1. **Start Celery Worker:**
   ```bash
   start_celery_worker.bat
   ```

2. **Start FastAPI Server:**
   ```bash
   start_fastapi.bat
   ```

3. **Test API:**
   ```bash
   python test_api_endpoint.py
   ```

## Lưu ý kỹ thuật

- Task sử dụng queue `default` 
- Progress được tính dựa trên số nodes thực tế cần xử lý
- Hỗ trợ cycle detection để tránh vòng lặp vô hạn
- Tương thích với hệ thống MongoDB task management hiện có
- Sử dụng cùng pattern với `quick_textbook_analysis` để đảm bảo consistency

## Kết quả

✅ API `generate_lesson_plan_content` đã được tích hợp thành công với Celery
✅ Hỗ trợ progress tracking chi tiết
✅ Tương thích với hệ thống task management hiện có
✅ Đã test và verify hoạt động đúng

Hệ thống giờ đây có thể xử lý lesson plan content generation bất đồng bộ với progress tracking tương tự như `quick_textbook_analysis`.
