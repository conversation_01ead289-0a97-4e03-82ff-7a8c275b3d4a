"""
Test API endpoint cho lesson plan content generation
"""
import requests
import json
import time


def test_lesson_plan_api():
    """Test API endpoint"""
    
    # Sample lesson plan data
    sample_data = {
        "lesson_plan_json": {
            "id": 1,
            "type": "SECTION",
            "title": "<PERSON><PERSON><PERSON> học Toán lớp 5",
            "content": "",
            "status": "ACTIVE",
            "children": [
                {
                    "id": 2,
                    "type": "PARAGRAPH",
                    "title": "Mục tiêu bài học",
                    "content": "",
                    "status": "ACTIVE",
                    "children": []
                },
                {
                    "id": 3,
                    "type": "PARAGRAPH",
                    "title": "Nội dung chính",
                    "content": "",
                    "status": "ACTIVE",
                    "children": []
                }
            ]
        },
        "lesson_id": "test_lesson_456"
    }
    
    # API endpoint
    api_url = "http://localhost:8000/api/v1/lesson-plan/generate-lesson-plan-content"
    
    print("🚀 Testing API endpoint...")
    print(f"URL: {api_url}")
    
    try:
        # Send POST request
        response = requests.post(
            api_url,
            json=sample_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API call successful!")
            print(f"Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # Extract task_id for monitoring
            task_id = result.get("task_id")
            if task_id:
                print(f"\n📊 Task ID: {task_id}")
                print("🔗 Monitor progress at:")
                print(f"   GET {result['endpoints']['check_status']}")
                print(f"   GET {result['endpoints']['get_result']}")
                
                # Test status endpoint
                status_url = f"http://localhost:8000{result['endpoints']['check_status']}"
                print(f"\n🔍 Testing status endpoint: {status_url}")
                
                status_response = requests.get(status_url)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print("✅ Status endpoint working!")
                    print(f"Status: {json.dumps(status_data, indent=2, ensure_ascii=False)}")
                else:
                    print(f"❌ Status endpoint failed: {status_response.status_code}")
            
            return True
            
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure FastAPI server is running on localhost:8000")
        print("💡 Start server with: start_fastapi.bat")
        return False
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False


if __name__ == "__main__":
    print("🧪 API Endpoint Test for Lesson Plan Content Generation")
    print("=" * 60)
    
    success = test_lesson_plan_api()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 API TEST PASSED!")
    else:
        print("❌ API TEST FAILED!")
        print("\n💡 Troubleshooting:")
        print("   1. Make sure FastAPI server is running: start_fastapi.bat")
        print("   2. Make sure Celery worker is running: start_celery_worker.bat")
        print("   3. Check server logs for errors")
