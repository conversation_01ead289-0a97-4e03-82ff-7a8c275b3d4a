# FlashDocs Integration - Tạo Slides từ Nội dung Bài học

## Tổng quan

Tích hợp FlashDocs API để tự động tạo slide presentations từ nội dung bài học trong hệ thống PlanBook AI.

## Tính năng

### 1. **Tạo Slides Tự động**
- Lấy nội dung bài học từ `textbook_retrieval_service`
- Tạo slides presentations sử dụng FlashDocs API
- Hỗ trợ cả Templates và Libraries

### 2. **Kiểm soát Nội dung**
- **Basic Mode**: Tạo slides từ prompt tự động
- **Custom Mode**: Hướng dẫn chi tiết cho từng slide
- **Quick Mode**: Tạo nhanh với cấu hình tối thiểu

### 3. **Tùy chỉnh Presentation**
- Sử dụng templates có sẵn từ FlashDocs
- Chọn từ library slides
- Tùy chỉnh số lượng slides
- Custom prompts cho nội dung

## Cài đặt & <PERSON><PERSON>u hình

### 1. **Đăng ký FlashDocs API**
```bash
# Đăng ký tại: https://www.flashdocs.com/api/signup
# Lấy API key từ dashboard
```

### 2. **Cấu hình Environment Variables**
```bash
# Thêm vào .env file
FLASHDOCS_API_KEY=your_flashdocs_api_key_here
```

### 3. **Dependencies**
Các packages cần thiết đã được thêm vào `requirements.txt`:
```
httpx>=0.25.0
pydantic>=2.0.0
fastapi>=0.95.0
```

## API Endpoints

### 1. **Tạo Slides Cơ bản**
```http
POST /api/v1/flashdocs/create-lesson-slides
Content-Type: application/json

{
    "lesson_id": "your_lesson_id",
    "template_id": "template_123",  // hoặc library_id
    "slide_count": 8,
    "custom_prompt": "Create engaging slides for high school students"
}
```

### 2. **Tạo Slides với Hướng dẫn Chi tiết**
```http
POST /api/v1/flashdocs/create-custom-slides
Content-Type: application/json

{
    "lesson_id": "your_lesson_id",
    "template_id": "template_123",
    "slide_instructions": [
        {
            "content": "Title slide with lesson overview",
            "layout": "centered title layout"
        },
        {
            "content": "Main concepts explanation with examples",
            "layout": "two-column layout with images"
        }
    ]
}
```

### 3. **Tạo Slides Nhanh**
```http
POST /api/v1/flashdocs/quick-lesson-slides/{lesson_id}?template_id=template_123&slide_count=6
```

### 4. **Kiểm tra Trạng thái**
```http
GET /api/v1/flashdocs/status/{task_id}
```

### 5. **Download Slides**
```http
GET /api/v1/flashdocs/download/{deck_id}?format=pptx
```

## Sử dụng trong Code

### 1. **Service Layer**
```python
from app.services.flashdocs_service import get_flashdocs_service

# Khởi tạo service
flashdocs_service = get_flashdocs_service()

# Tạo slides từ lesson
result = await flashdocs_service.create_lesson_slides_from_content(
    lesson_id="lesson_123",
    template_id="template_456",
    slide_count=8
)
```

### 2. **Custom Slides với Hướng dẫn**
```python
slide_instructions = [
    {
        "content": "Introduction to the topic",
        "layout": "title slide layout"
    },
    {
        "content": "Key concepts and definitions",
        "layout": "bullet points with images"
    }
]

result = await flashdocs_service.create_custom_lesson_slides(
    lesson_id="lesson_123",
    slide_instructions=slide_instructions,
    template_id="template_456"
)
```

## Quy trình Hoạt động

### 1. **Lấy Nội dung Bài học**
```
lesson_id → textbook_retrieval_service → lesson_content + metadata
```

### 2. **Tạo Prompt**
```
lesson_content + metadata → generate_lesson_prompt() → structured_prompt
```

### 3. **Gọi FlashDocs API**
```
structured_prompt + template/library → FlashDocs API → slide_deck
```

### 4. **Trả về Kết quả**
```
slide_deck → API response → download_url + task_id
```

## Templates vs Libraries

### **Templates**
- Bộ slides cố định với placeholders
- Thích hợp cho cấu trúc lesson chuẩn
- Tất cả slides trong template được sử dụng
- Placeholders được điền tự động

### **Libraries**
- Bộ sưu tập slides đa dạng
- AI chọn slides phù hợp nhất
- Linh hoạt về số lượng và loại slides
- Sử dụng embeddings để matching

## Error Handling

### 1. **API Key Issues**
```python
if not self.api_key:
    raise HTTPException(
        status_code=500, 
        detail="FlashDocs API key not configured"
    )
```

### 2. **Lesson Not Found**
```python
if not lesson_data:
    raise HTTPException(
        status_code=404,
        detail=f"Lesson not found: {lesson_id}"
    )
```

### 3. **FlashDocs API Errors**
```python
try:
    response = await client.post(url, headers=headers, json=data)
    response.raise_for_status()
except httpx.HTTPError as e:
    raise HTTPException(
        status_code=500,
        detail=f"FlashDocs API request failed: {str(e)}"
    )
```

## Testing

### 1. **Chạy Test Script**
```bash
python test_flashdocs_integration.py
```

### 2. **Test API Endpoints**
```bash
# Start server
python -m uvicorn app.main:app --reload

# Test endpoints với curl hoặc Postman
curl -X POST "http://localhost:8000/api/v1/flashdocs/quick-lesson-slides/lesson_123" \
     -H "Content-Type: application/json"
```

## Monitoring & Logging

### 1. **Service Logs**
```python
logger.info(f"Successfully created slides for lesson: {lesson_id}")
logger.error(f"Error creating lesson slides: {str(e)}")
```

### 2. **API Response Tracking**
```python
return {
    "success": True,
    "message": "Slides created successfully",
    "data": result,
    "lesson_id": lesson_id,
    "slide_count": slide_count
}
```

## Best Practices

### 1. **Content Optimization**
- Giới hạn content length để tránh quá tải prompt
- Tối ưu hóa metadata cho context tốt hơn
- Sử dụng structured prompts

### 2. **Error Recovery**
- Retry logic cho network issues
- Fallback options khi API fails
- Graceful degradation

### 3. **Performance**
- Async operations cho all API calls
- Caching cho repeated requests
- Background tasks cho large presentations

## Troubleshooting

### 1. **Common Issues**
```
Issue: "API key not configured"
Solution: Set FLASHDOCS_API_KEY environment variable

Issue: "Template/Library not found"
Solution: Verify template_id/library_id trong FlashDocs dashboard

Issue: "Lesson content not found"
Solution: Check lesson_id exists trong textbook_retrieval_service
```

### 2. **Debug Mode**
```python
# Enable debug logging
import logging
logging.getLogger("app.services.flashdocs_service").setLevel(logging.DEBUG)
```

## Roadmap

### 1. **Planned Features**
- [ ] Batch slide creation cho multiple lessons
- [ ] Template management integration
- [ ] Advanced slide customization options
- [ ] Integration với presentation scheduling

### 2. **Performance Improvements**
- [ ] Caching cho frequently used templates
- [ ] Async batch processing
- [ ] Optimized content chunking

---

## Support

Để được hỗ trợ:
1. Check logs trong `logs/planbook_ai.log`
2. Verify API key và permissions
3. Test với simple examples trước
4. Check FlashDocs API documentation: https://docs.flashdocs.com/
